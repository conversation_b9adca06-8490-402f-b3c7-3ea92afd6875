-- Metamorphic Reactor Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Agent logs table for tracking dual-agent iterations
CREATE TABLE agent_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL,
    iteration INTEGER NOT NULL,
    plan TEXT NOT NULL,
    critique TEXT NOT NULL,
    score DECIMAL(3,2) NOT NULL CHECK (score >= 0 AND score <= 1),
    patch JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for performance
    INDEX idx_agent_logs_session_id (session_id),
    INDEX idx_agent_logs_created_at (created_at),
    INDEX idx_agent_logs_score (score)
);

-- Sessions table for tracking reactor runs
CREATE TABLE reactor_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prompt TEXT NOT NULL,
    max_loops INTEGER NOT NULL DEFAULT 10,
    final_score DECIMAL(3,2),
    final_patch JSONB,
    iterations_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed')),
    github_pr_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Indexes
    INDEX idx_reactor_sessions_status (status),
    INDEX idx_reactor_sessions_created_at (created_at)
);

-- Add foreign key constraint
ALTER TABLE agent_logs 
ADD CONSTRAINT fk_agent_logs_session_id 
FOREIGN KEY (session_id) REFERENCES reactor_sessions(id) ON DELETE CASCADE;

-- Row Level Security (RLS) policies
ALTER TABLE agent_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactor_sessions ENABLE ROW LEVEL SECURITY;

-- For now, allow all operations (in production, implement proper user-based policies)
CREATE POLICY "Allow all operations on agent_logs" ON agent_logs FOR ALL USING (true);
CREATE POLICY "Allow all operations on reactor_sessions" ON reactor_sessions FOR ALL USING (true);

import { useState, useEffect, useCallback } from 'react';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface GitHubUser {
  login: string;
  name: string;
  avatar_url: string;
}

interface UseGitHubAuthReturn {
  isConnected: boolean;
  user: GitHubUser | null;
  isLoading: boolean;
  connectGitHub: () => void;
  disconnectGitHub: () => Promise<void>;
  checkConnection: () => Promise<void>;
}

export const useGitHubAuth = (): UseGitHubAuthReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [user, setUser] = useState<GitHubUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkConnection = useCallback(async () => {
    try {
      if (!isSupabaseConfigured()) {
        console.warn('Supabase not configured - GitHub auth unavailable');
        setIsConnected(false);
        setUser(null);
        setIsLoading(false);
        return;
      }

      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setIsConnected(false);
        setUser(null);
        setIsLoading(false);
        return;
      }

      const response = await fetch('/functions/v1/github-oauth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setIsConnected(data.hasToken);
        if (data.hasToken && data.user) {
          setUser(data.user);
        }
      } else {
        setIsConnected(false);
        setUser(null);
      }
    } catch (error) {
      console.error('Error checking GitHub connection:', error);
      setIsConnected(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const connectGitHub = useCallback(() => {
    // Generate state for OAuth security
    const state = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('github_oauth_state', state);

    // Redirect to GitHub OAuth
    const clientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
    const redirectUri = `${window.location.origin}/auth/github/callback`;
    const scope = 'repo,user:email';
    
    const authUrl = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${state}`;
    
    window.location.href = authUrl;
  }, []);

  const disconnectGitHub = useCallback(async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      // Delete GitHub token from database
      await supabase
        .from('github_tokens')
        .delete()
        .eq('user_id', session.user.id);

      setIsConnected(false);
      setUser(null);
    } catch (error) {
      console.error('Error disconnecting GitHub:', error);
      throw error;
    }
  }, []);

  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  return {
    isConnected,
    user,
    isLoading,
    connectGitHub,
    disconnectGitHub,
    checkConnection,
  };
};

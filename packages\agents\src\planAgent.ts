import { J<PERSON><PERSON><PERSON>, PlanRequest, AgentConfig } from './types.js';

export class PlanAgent {
  private config: AgentConfig;

  constructor(config: AgentConfig = {
    model: 'gemini-2.5',
    temperature: 0.7,
    maxTokens: 2000
  }) {
    this.config = config;
  }

  async generatePatch(request: PlanRequest): Promise<JSONPatch> {
    try {
      // Validate request
      if (!request.prompt || request.prompt.trim().length === 0) {
        throw new Error('Prompt cannot be empty');
      }

      // Simulate AI planning logic
      // In a real implementation, this would call Gemini 2.5 or GPT-4 Turbo
      const planningPrompt = this.buildPlanningPrompt(request);
      
      // Mock response for now - replace with actual AI call
      const mockPatch: JSONPatch = {
        operations: [
          {
            op: 'add',
            path: '/newFeature',
            value: {
              name: 'Generated Feature',
              description: `Feature generated from prompt: ${request.prompt}`,
              timestamp: new Date().toISOString()
            }
          }
        ],
        description: `Plan generated for: ${request.prompt}`,
        confidence: 0.8
      };

      return mockPatch;
    } catch (error) {
      console.error('Error generating patch:', error);
      throw new Error(`Plan generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildPlanningPrompt(request: PlanRequest): string {
    let prompt = `You are a code planning agent. Generate a JSON patch to implement the following request:\n\n`;
    prompt += `Request: ${request.prompt}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    if (request.previousAttempts && request.previousAttempts.length > 0) {
      prompt += `Previous attempts (learn from these):\n`;
      request.previousAttempts.forEach((attempt, index) => {
        prompt += `Attempt ${index + 1}: ${attempt.description}\n`;
      });
      prompt += '\n';
    }
    
    prompt += `Generate a JSON patch that implements the requested changes. `;
    prompt += `Focus on being precise, maintainable, and following best practices. `;
    prompt += `Include a confidence score (0-1) and detailed description.`;
    
    return prompt;
  }

  async validatePatch(patch: JSONPatch): Promise<boolean> {
    // Basic validation
    if (!patch.operations || !Array.isArray(patch.operations)) {
      return false;
    }
    
    if (!patch.description || typeof patch.description !== 'string') {
      return false;
    }
    
    if (typeof patch.confidence !== 'number' || patch.confidence < 0 || patch.confidence > 1) {
      return false;
    }
    
    // Validate each operation
    for (const op of patch.operations) {
      if (!op.op || !op.path) {
        return false;
      }
      
      if (!['add', 'remove', 'replace', 'move', 'copy', 'test'].includes(op.op)) {
        return false;
      }
    }
    
    return true;
  }
}

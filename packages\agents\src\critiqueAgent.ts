import { <PERSON><PERSON><PERSON>atch, CritiqueRequest, CritiqueResult, AgentConfig } from './types.js';

export class CritiqueAgent {
  private config: AgentConfig;

  constructor(config: AgentConfig = {
    model: 'gemini-2.5',
    temperature: 0.3,
    maxTokens: 1500
  }) {
    this.config = config;
  }

  async scorePatch(request: CritiqueRequest): Promise<CritiqueResult> {
    try {
      const critiquePrompt = this.buildCritiquePrompt(request);
      
      // Mock critique logic - replace with actual AI call
      const score = this.calculateMockScore(request.patch);
      const feedback = this.generateMockFeedback(request.patch, score);
      const suggestions = this.generateMockSuggestions(request.patch, score);
      
      return {
        score,
        feedback,
        suggestions,
        isAcceptable: score >= 0.95
      };
    } catch (error) {
      console.error('Error scoring patch:', error);
      throw new Error(`Critique generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildCritiquePrompt(request: CritiqueRequest): string {
    let prompt = `You are a code critique agent. Evaluate the following JSON patch:\n\n`;
    prompt += `Original Request: ${request.originalPrompt}\n\n`;
    prompt += `Proposed Patch: ${JSON.stringify(request.patch, null, 2)}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    prompt += `Evaluate this patch on the following criteria:\n`;
    prompt += `1. Correctness: Does it implement the requested functionality?\n`;
    prompt += `2. Completeness: Are all requirements addressed?\n`;
    prompt += `3. Quality: Is the code well-structured and maintainable?\n`;
    prompt += `4. Safety: Are there any potential issues or side effects?\n`;
    prompt += `5. Best Practices: Does it follow coding standards?\n\n`;
    prompt += `Provide a score from 0.0 to 1.0, detailed feedback, and suggestions for improvement.`;
    
    return prompt;
  }

  private calculateMockScore(patch: JSONPatch): number {
    // Mock scoring logic based on patch characteristics
    let score = 0.5; // Base score
    
    // Boost score based on patch quality indicators
    if (patch.description && patch.description.length > 20) {
      score += 0.1;
    }
    
    if (patch.confidence && patch.confidence > 0.7) {
      score += 0.2;
    }
    
    if (patch.operations && patch.operations.length > 0) {
      score += 0.1;
    }
    
    // Check for good operation structure
    const hasValidOps = patch.operations.every(op => 
      op.op && op.path && ['add', 'remove', 'replace', 'move', 'copy', 'test'].includes(op.op)
    );
    
    if (hasValidOps) {
      score += 0.1;
    }
    
    // Add some randomness to simulate AI variability
    score += (Math.random() - 0.5) * 0.1;
    
    return Math.min(Math.max(score, 0), 1);
  }

  private generateMockFeedback(patch: JSONPatch, score: number): string {
    if (score >= 0.95) {
      return "Excellent patch! The implementation is comprehensive, well-structured, and addresses all requirements effectively.";
    } else if (score >= 0.8) {
      return "Good patch with solid implementation. Minor improvements could enhance quality and maintainability.";
    } else if (score >= 0.6) {
      return "Decent patch but needs improvements in implementation details and error handling.";
    } else if (score >= 0.4) {
      return "Patch has significant issues that need to be addressed before it can be considered acceptable.";
    } else {
      return "Poor patch quality. Major revisions needed to meet requirements and standards.";
    }
  }

  private generateMockSuggestions(patch: JSONPatch, score: number): string[] {
    const suggestions: string[] = [];
    
    if (score < 0.95) {
      suggestions.push("Add more comprehensive error handling");
      suggestions.push("Include input validation for all parameters");
    }
    
    if (score < 0.8) {
      suggestions.push("Improve code documentation and comments");
      suggestions.push("Consider edge cases and boundary conditions");
    }
    
    if (score < 0.6) {
      suggestions.push("Refactor for better maintainability");
      suggestions.push("Add unit tests for new functionality");
    }
    
    if (score < 0.4) {
      suggestions.push("Reconsider the overall approach");
      suggestions.push("Break down complex operations into smaller steps");
    }
    
    return suggestions;
  }

  async validateCritique(critique: CritiqueResult): Promise<boolean> {
    // Validate critique structure
    if (typeof critique.score !== 'number' || critique.score < 0 || critique.score > 1) {
      return false;
    }
    
    if (!critique.feedback || typeof critique.feedback !== 'string') {
      return false;
    }
    
    if (!Array.isArray(critique.suggestions)) {
      return false;
    }
    
    if (typeof critique.isAcceptable !== 'boolean') {
      return false;
    }
    
    return true;
  }
}

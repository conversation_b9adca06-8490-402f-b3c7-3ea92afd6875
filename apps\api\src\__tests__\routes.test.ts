import request from 'supertest';
import express from 'express';
import { loopRouter } from '../routes/loop.js';

// Mock the services
jest.mock('../services/reactorLoop.js');
jest.mock('../services/supabase.js');
jest.mock('../services/githubService.js');

const app = express();
app.use(express.json());
app.use('/api', loopRouter);

describe('Loop API Routes', () => {
  describe('POST /api/loop', () => {
    it('should accept valid loop request', async () => {
      const validRequest = {
        prompt: 'Optimize this function',
        maxLoops: 5,
        createPR: false
      };

      const response = await request(app)
        .post('/api/loop')
        .send(validRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
    });

    it('should reject request without prompt', async () => {
      const invalidRequest = {
        maxLoops: 5
      };

      const response = await request(app)
        .post('/api/loop')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation error');
    });

    it('should reject request with invalid maxLoops', async () => {
      const invalidRequest = {
        prompt: 'Test prompt',
        maxLoops: 100 // Too high
      };

      const response = await request(app)
        .post('/api/loop')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle empty prompt', async () => {
      const invalidRequest = {
        prompt: '',
        maxLoops: 5
      };

      const response = await request(app)
        .post('/api/loop')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/loop/:sessionId', () => {
    it('should reject invalid session ID format', async () => {
      const response = await request(app)
        .get('/api/loop/invalid-id')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid session ID');
    });

    it('should accept valid UUID format', async () => {
      const validUuid = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .get(`/api/loop/${validUuid}`)
        .expect(404); // Will be 404 since session doesn't exist in test

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/loop/:sessionId/progress', () => {
    it('should reject invalid session ID', async () => {
      const response = await request(app)
        .get('/api/loop/invalid/progress')
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should accept valid session ID format', async () => {
      const validUuid = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .get(`/api/loop/${validUuid}/progress`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
    });
  });

  describe('POST /api/loop/stream', () => {
    it('should accept streaming request', async () => {
      const validRequest = {
        prompt: 'Stream test',
        maxLoops: 3
      };

      const response = await request(app)
        .post('/api/loop/stream')
        .send(validRequest)
        .expect(200);

      expect(response.headers['content-type']).toContain('text/event-stream');
    });

    it('should reject invalid streaming request', async () => {
      const invalidRequest = {
        maxLoops: 3
        // Missing prompt
      };

      const response = await request(app)
        .post('/api/loop/stream')
        .send(invalidRequest);

      // Should still return 200 but with error in stream
      expect(response.status).toBe(200);
    });
  });

  describe('POST /api/loop/:sessionId/pr', () => {
    it('should reject invalid session ID for PR creation', async () => {
      const response = await request(app)
        .post('/api/loop/invalid-id/pr')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid session ID');
    });

    it('should handle valid session ID format', async () => {
      const validUuid = '123e4567-e89b-12d3-a456-************';
      
      const response = await request(app)
        .post(`/api/loop/${validUuid}/pr`)
        .expect(404); // Session not found in test

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Session not found');
    });
  });
});

describe('Health Check', () => {
  const healthApp = express();
  healthApp.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  it('should return health status', async () => {
    const response = await request(healthApp)
      .get('/health')
      .expect(200);

    expect(response.body.status).toBe('ok');
    expect(response.body.timestamp).toBeDefined();
  });
});

import { Operation } from 'fast-json-patch';

export interface JSONPatch {
  operations: Operation[];
  description: string;
  confidence: number;
}

export interface PlanRequest {
  prompt: string;
  context?: Record<string, any>;
  previousAttempts?: JSONPatch[];
}

export interface CritiqueRequest {
  patch: JSONPatch;
  originalPrompt: string;
  context?: Record<string, any>;
}

export interface CritiqueResult {
  score: number; // 0-1 scale
  feedback: string;
  suggestions: string[];
  isAcceptable: boolean;
}

export interface AgentConfig {
  model: 'gemini-2.5' | 'gpt-4-turbo';
  temperature: number;
  maxTokens: number;
}

export interface LoopResult {
  finalPatch: JSONPatch;
  score: number;
  iterations: number;
  logs: Array<{
    iteration: number;
    plan: string;
    critique: string;
    score: number;
    patch: JSONPatch;
  }>;
}


import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { Settings, History, ArrowLeft, RotateCcw, Download, BookOpen, Keyboard } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { CodeEditor } from "@/components/CodeEditor";
import { DiffViewer } from "@/components/DiffViewer";
import { AgentLog } from "@/components/AgentLog";
import { ControlPanel } from "@/components/ControlPanel";
import { CodeExamples } from "@/components/CodeExamples";
import { HelpPanel } from "@/components/HelpPanel";
import { useCodeTransformation } from "@/hooks/useCodeTransformation";
import { useReactorLoop } from "@/hooks/useReactorLoop";
import { useTransformationHistory } from "@/hooks/useTransformationHistory";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { StreamPanel } from "@/components/StreamPanel";

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentPrompt, setCurrentPrompt] = useState(`// Welcome to Metamorphic Reactor
// Describe what you want to transform or improve in your code

Optimize this fibonacci function for better performance and add memoization.

function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`);

  const [showExamples, setShowExamples] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [activePanel, setActivePanel] = useState<'stream' | 'logs'>('stream');

  // Legacy transformation hook (keeping for compatibility)
  const {
    isRunning: isLegacyRunning,
    logs,
    diffContent,
    transformedCode,
    runTransformation,
    stopTransformation,
    clearLogs
  } = useCodeTransformation();

  // New reactor loop hook
  const {
    isRunning: isReactorRunning,
    events,
    currentPatch,
    finalResult,
    progress,
    runLoop,
    stopLoop,
    clearEvents
  } = useReactorLoop();

  const isRunning = isLegacyRunning || isReactorRunning;

  const { addToHistory } = useTransformationHistory();

  const handleRunLoop = async () => {
    try {
      await runLoop(currentPrompt, 10);
      toast({
        title: "Reactor Loop Started",
        description: "The dual-agent system is analyzing and transforming your code.",
      });
    } catch (error) {
      toast({
        title: "Reactor Error",
        description: "An error occurred during the reactor loop process.",
        variant: "destructive",
      });
    }
  };

  const handleStopLoop = () => {
    stopLoop();
    stopTransformation();
    toast({
      title: "Loop Stopped",
      description: "The reactor loop has been stopped.",
    });
  };

  const handleApplyChanges = () => {
    const codeToApply = transformedCode || (finalResult?.finalPatch ? 'Applied patch from reactor' : null);

    if (codeToApply) {
      // Save to history before applying
      addToHistory({
        originalCode: currentPrompt,
        transformedCode: codeToApply,
        iterations: finalResult?.iterations || 5,
        finalScore: finalResult?.score || 0.95,
        title: `Reactor ${new Date().toLocaleTimeString()}`
      });

      setCurrentPrompt(codeToApply);

      toast({
        title: "Changes Applied",
        description: "The reactor transformations have been applied.",
      });

      // Clear diff after applying
      setTimeout(() => {
        clearLogs();
        clearEvents();
      }, 1000);
    }
  };

  const handleSelectExample = (code: string) => {
    setCurrentPrompt(code);
    setShowExamples(false);
    toast({
      title: "Example Loaded",
      description: "Code example has been loaded into the editor.",
    });
  };

  const handleDownloadCode = () => {
    const codeToDownload = transformedCode || currentPrompt;
    const patchData = finalResult?.finalPatch ? JSON.stringify(finalResult.finalPatch, null, 2) : null;

    const content = patchData
      ? `// Original Prompt:\n${currentPrompt}\n\n// Generated Patch:\n${patchData}`
      : codeToDownload;

    const blob = new Blob([content], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = patchData ? 'reactor-patch.json' : 'optimized-code.js';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Content Downloaded",
      description: "Your reactor output has been saved to your downloads folder.",
    });
  };

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    onRunTransformation: handleRunLoop,
    onStopTransformation: handleStopLoop,
    onClearLogs: () => {
      clearLogs();
      clearEvents();
    },
    onApplyChanges: handleApplyChanges
  });

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold text-white">Metamorphic Reactor</h1>
            <Badge variant="secondary" className="bg-indigo-600/20 text-indigo-300 border-indigo-500/30">
              Dashboard
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExamples(!showExamples)}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <BookOpen className="w-4 h-4 mr-2" />
              Examples
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(!showHelp)}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <Keyboard className="w-4 h-4 mr-2" />
              Help
            </Button>
            {(transformedCode || finalResult) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleApplyChanges}
                className="border-green-600 text-green-300 hover:bg-green-900/20"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Apply Changes
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadCode}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/history')}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <History className="w-4 h-4 mr-2" />
              History
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/settings')}
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="h-[calc(100vh-73px)]">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Code Editor */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-slate-800">
                <h2 className="text-lg font-semibold text-white">Code Editor</h2>
                <ControlPanel 
                  isRunning={isRunning}
                  onRunLoop={handleRunLoop}
                  onStop={stopTransformation}
                />
              </div>
              <div className="flex-1">
                <CodeEditor
                  value={currentCode}
                  onChange={setCurrentCode}
                  language="javascript"
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-slate-800 hover:bg-slate-700" />

          {/* Center Panel - Diff Viewer */}
          <ResizablePanel defaultSize={35} minSize={25}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-slate-800">
                <h2 className="text-lg font-semibold text-white">Live Diff</h2>
                {diffContent && (
                  <Badge className="bg-green-600/20 text-green-300 border-green-500/30">
                    Patch Ready
                  </Badge>
                )}
              </div>
              <div className="flex-1">
                <DiffViewer diffContent={diffContent} />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-slate-800 hover:bg-slate-700" />

          {/* Right Panel - Agent Logs & Utilities */}
          <ResizablePanel defaultSize={25} minSize={20}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-slate-800">
                <h2 className="text-lg font-semibold text-white">
                  {showExamples ? 'Code Examples' : showHelp ? 'Help' : 'Agent Logs'}
                </h2>
                {isRunning && !showExamples && !showHelp && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-400">Running</span>
                  </div>
                )}
              </div>
              <div className="flex-1 overflow-hidden">
                {showExamples ? (
                  <div className="h-full overflow-y-auto">
                    <CodeExamples onSelectExample={handleSelectExample} />
                  </div>
                ) : showHelp ? (
                  <div className="p-4">
                    <HelpPanel />
                  </div>
                ) : (
                  <AgentLog logs={logs} />
                )}
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
};

export default Dashboard;

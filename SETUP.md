# 🚀 Metamorphic Reactor Setup Guide

This guide will help you set up the Metamorphic Reactor with real AI processing and GitHub integration.

## Quick Start (Development Mode)

The application works out of the box with mock responses for development:

```bash
npm install
npm run dev
```

Visit http://localhost:5173 and you'll see a configuration status banner. The reactor will work with mock AI responses until you configure the services below.

## Production Setup

### 1. Supabase Configuration

#### Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Click "New Project"
3. Choose your organization and create the project
4. Wait for the project to be ready

#### Get Your Credentials
1. Go to Settings > API in your Supabase dashboard
2. Copy your Project URL and anon public key

#### Set Environment Variables
Create a `.env` file in `apps/web/`:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# GitHub OAuth Configuration (optional)
VITE_GITHUB_CLIENT_ID=your-github-client-id

# API Configuration
VITE_API_URL=http://localhost:3001/api
```

#### Deploy Edge Functions
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Set secrets for Edge Functions
supabase secrets set OPENAI_API_KEY=your-openai-key
supabase secrets set ANTHROPIC_API_KEY=your-anthropic-key
supabase secrets set GITHUB_CLIENT_ID=your-github-client-id
supabase secrets set GITHUB_CLIENT_SECRET=your-github-client-secret

# Deploy Edge Functions
supabase functions deploy ai-loop
supabase functions deploy github-oauth
supabase functions deploy create-pr
```

### 2. GitHub OAuth (Optional)

#### Create GitHub OAuth App
1. Go to [GitHub Developer Settings](https://github.com/settings/applications/new)
2. Fill in the application details:
   - **Application name**: Metamorphic Reactor
   - **Homepage URL**: http://localhost:5173
   - **Authorization callback URL**: http://localhost:5173/auth/github/callback
3. Click "Register application"
4. Copy the Client ID and generate a Client Secret

#### Update Environment Variables
Add your GitHub credentials to `.env`:

```bash
VITE_GITHUB_CLIENT_ID=your-github-client-id
```

And set the secret in Supabase:
```bash
supabase secrets set GITHUB_CLIENT_SECRET=your-github-client-secret
```

### 3. AI API Keys

#### OpenAI API Key
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. Set it in Supabase: `supabase secrets set OPENAI_API_KEY=your-key`

#### Anthropic API Key (Fallback)
1. Go to [Anthropic Console](https://console.anthropic.com/)
2. Create a new API key
3. Set it in Supabase: `supabase secrets set ANTHROPIC_API_KEY=your-key`

## Verification

After setup, restart your development server:

```bash
npm run dev
```

You should see:
- ✅ Configuration status shows all services as configured
- ✅ Reactor loop uses real AI processing
- ✅ GitHub connection available in the UI
- ✅ PR creation works after running a successful loop

## Troubleshooting

### Common Issues

**"Missing Supabase environment variables"**
- Make sure you created the `.env` file in `apps/web/`
- Check that your Supabase URL and key are correct
- Restart the development server after adding environment variables

**"GitHub OAuth not configured"**
- This is optional - the app works without it
- Make sure your GitHub Client ID is in the `.env` file
- Check that the callback URL matches in your GitHub OAuth app

**"Edge Functions not working"**
- Make sure you deployed the functions: `supabase functions deploy ai-loop`
- Check that your API keys are set as secrets in Supabase
- Verify your Supabase project is linked correctly

**"AI processing fails"**
- Check that your OpenAI API key is valid and has credits
- Verify the Anthropic API key is set as a fallback
- Check the Edge Function logs in your Supabase dashboard

### Development vs Production

**Development Mode:**
- Uses mock AI responses when Supabase isn't configured
- Shows configuration status banner
- All features work with placeholder data

**Production Mode:**
- Requires Supabase configuration for AI processing
- Requires GitHub OAuth for PR creation
- Real API calls to OpenAI and Anthropic

## Support

If you encounter issues:
1. Check the configuration status banner in the app
2. Review the browser console for errors
3. Check Supabase Edge Function logs
4. Verify all environment variables are set correctly

The application is designed to work gracefully with partial configuration, so you can set up services incrementally.

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertTriangle, ExternalLink, Copy } from 'lucide-react';
import { isSupabaseConfigured } from '@/lib/supabase';

export const ConfigurationStatus = () => {
  const [showDetails, setShowDetails] = useState(false);
  const [copied, setCopied] = useState(false);

  const supabaseConfigured = isSupabaseConfigured();
  const githubClientId = import.meta.env.VITE_GITHUB_CLIENT_ID;
  const githubConfigured = githubClientId && githubClientId !== 'your-github-client-id';

  const handleCopyEnvTemplate = async () => {
    const envTemplate = `# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# GitHub OAuth Configuration
VITE_GITHUB_CLIENT_ID=your-github-client-id

# API Configuration
VITE_API_URL=http://localhost:3001/api`;

    try {
      await navigator.clipboard.writeText(envTemplate);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getStatusIcon = (configured: boolean) => {
    if (configured) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusBadge = (configured: boolean, label: string) => {
    return (
      <Badge 
        variant={configured ? "default" : "destructive"}
        className={configured ? "bg-green-600/20 text-green-300 border-green-500/30" : ""}
      >
        {getStatusIcon(configured)}
        <span className="ml-1">{label}</span>
      </Badge>
    );
  };

  if (supabaseConfigured && githubConfigured) {
    return null; // Don't show when everything is configured
  }

  return (
    <Card className="mb-4 border-amber-500/30 bg-amber-900/10">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-amber-500" />
            <CardTitle className="text-lg">Configuration Required</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="text-amber-300 hover:text-amber-200"
          >
            {showDetails ? 'Hide Details' : 'Show Setup'}
          </Button>
        </div>
        <CardDescription>
          Some services need configuration to work properly.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          {getStatusBadge(supabaseConfigured, 'Supabase')}
          {getStatusBadge(githubConfigured, 'GitHub OAuth')}
        </div>

        {!supabaseConfigured && (
          <Alert className="border-amber-500/30 bg-amber-900/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Supabase not configured:</strong> The reactor will use mock responses. 
              Configure Supabase for real AI processing with GPT-4 and Claude.
            </AlertDescription>
          </Alert>
        )}

        {!githubConfigured && (
          <Alert className="border-amber-500/30 bg-amber-900/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>GitHub OAuth not configured:</strong> PR creation will be unavailable. 
              Configure GitHub OAuth to enable automatic pull request creation.
            </AlertDescription>
          </Alert>
        )}

        {showDetails && (
          <div className="space-y-4 pt-4 border-t border-slate-700">
            <div>
              <h4 className="font-medium text-white mb-2">Quick Setup</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm text-slate-300">
                <li>
                  Create a Supabase project at{' '}
                  <a 
                    href="https://supabase.com" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-indigo-400 hover:text-indigo-300 inline-flex items-center"
                  >
                    supabase.com
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </li>
                <li>
                  Create a GitHub OAuth App at{' '}
                  <a 
                    href="https://github.com/settings/applications/new" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-indigo-400 hover:text-indigo-300 inline-flex items-center"
                  >
                    GitHub Developer Settings
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                </li>
                <li>Copy the environment template below to your <code className="bg-slate-800 px-1 rounded">.env</code> file</li>
                <li>Replace the placeholder values with your actual credentials</li>
                <li>Restart the development server</li>
              </ol>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-white">Environment Template</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyEnvTemplate}
                  className="text-xs"
                >
                  <Copy className="w-3 h-3 mr-1" />
                  {copied ? 'Copied!' : 'Copy'}
                </Button>
              </div>
              <pre className="bg-slate-800 p-3 rounded text-xs text-slate-300 overflow-x-auto">
{`# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# GitHub OAuth Configuration
VITE_GITHUB_CLIENT_ID=your-github-client-id

# API Configuration
VITE_API_URL=http://localhost:3001/api`}
              </pre>
            </div>

            <Alert className="border-indigo-500/30 bg-indigo-900/10">
              <AlertDescription className="text-sm">
                <strong>Note:</strong> The application will work in development mode with mock responses 
                even without configuration. Configure the services above for full functionality.
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

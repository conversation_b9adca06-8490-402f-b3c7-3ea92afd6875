import { Plan<PERSON><PERSON>, <PERSON>rit<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LoopR<PERSON>ult } from '@metamorphic-reactor/agents';
import { supabaseService, ReactorSession } from './supabase.js';
import { createGitHubService, PullRequestResult } from './githubService.js';

export interface LoopRequest {
  prompt: string;
  maxLoops?: number;
  context?: Record<string, any>;
  createPR?: boolean;
}

export interface LoopProgress {
  sessionId: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch: JSONPatch;
  isComplete: boolean;
}

export class ReactorLoopService {
  private planAgent: PlanAgent;
  private critiqueAgent: CritiqueAgent;
  private githubService: ReturnType<typeof createGitHubService>;

  constructor() {
    this.planAgent = new PlanAgent({
      model: 'gemini-2.5',
      temperature: 0.7,
      maxTokens: 2000
    });

    this.critiqueAgent = new CritiqueAgent({
      model: 'gemini-2.5',
      temperature: 0.3,
      maxTokens: 1500
    });

    this.githubService = createGitHubService();
  }

  async runLoop(
    request: LoopRequest,
    onProgress?: (progress: LoopProgress) => void
  ): Promise<LoopResult> {
    const maxLoops = request.maxLoops || 10;
    const targetScore = 0.95;
    
    // Create session in database
    const session = await supabaseService.createSession({
      prompt: request.prompt,
      max_loops: maxLoops,
      status: 'running'
    });

    const sessionId = session.id!;
    const logs: LoopResult['logs'] = [];
    let currentPatch: JSONPatch | null = null;
    let bestScore = 0;
    let bestPatch: JSONPatch | null = null;

    try {
      for (let iteration = 1; iteration <= maxLoops; iteration++) {
        console.log(`🔄 Starting iteration ${iteration}/${maxLoops}`);

        // Plan phase
        const planRequest = {
          prompt: request.prompt,
          context: request.context,
          previousAttempts: currentPatch ? [currentPatch] : undefined
        };

        currentPatch = await this.planAgent.generatePatch(planRequest);
        const planDescription = `Iteration ${iteration}: ${currentPatch.description}`;

        // Critique phase
        const critiqueRequest = {
          patch: currentPatch,
          originalPrompt: request.prompt,
          context: request.context
        };

        const critique = await this.critiqueAgent.scorePatch(critiqueRequest);
        const critiqueDescription = `Score: ${critique.score.toFixed(3)} - ${critique.feedback}`;

        // Log iteration
        const logEntry = {
          session_id: sessionId,
          iteration,
          plan: planDescription,
          critique: critiqueDescription,
          score: critique.score,
          patch: currentPatch
        };

        await supabaseService.logIteration(logEntry);

        // Track best result
        if (critique.score > bestScore) {
          bestScore = critique.score;
          bestPatch = currentPatch;
        }

        // Add to logs
        logs.push({
          iteration,
          plan: planDescription,
          critique: critiqueDescription,
          score: critique.score,
          patch: currentPatch
        });

        // Notify progress
        if (onProgress) {
          onProgress({
            sessionId,
            iteration,
            plan: planDescription,
            critique: critiqueDescription,
            score: critique.score,
            patch: currentPatch,
            isComplete: critique.score >= targetScore
          });
        }

        // Check if we've reached the target score
        if (critique.score >= targetScore) {
          console.log(`✅ Target score reached: ${critique.score.toFixed(3)}`);
          break;
        }

        console.log(`📊 Iteration ${iteration} score: ${critique.score.toFixed(3)}`);
      }

      // Create GitHub PR if requested and successful
      let prUrl: string | undefined;
      if (request.createPR && bestPatch && this.githubService && bestScore >= 0.8) {
        try {
          console.log('🔀 Creating GitHub PR for successful reactor run...');
          const prResult = await this.githubService.createReactorPR(
            sessionId,
            bestPatch,
            request.prompt,
            bestScore,
            logs.length
          );
          prUrl = prResult.url;
          console.log(`✅ GitHub PR created: ${prUrl}`);
        } catch (error) {
          console.error('❌ Failed to create GitHub PR:', error);
          // Don't fail the entire loop if PR creation fails
        }
      }

      // Update session with final results
      await supabaseService.updateSession(sessionId, {
        final_score: bestScore,
        final_patch: bestPatch,
        iterations_count: logs.length,
        status: 'completed',
        completed_at: new Date().toISOString(),
        github_pr_url: prUrl
      });

      return {
        finalPatch: bestPatch!,
        score: bestScore,
        iterations: logs.length,
        logs,
        prUrl
      };

    } catch (error) {
      console.error('❌ Loop execution failed:', error);
      
      // Update session with error status
      await supabaseService.updateSession(sessionId, {
        status: 'failed',
        completed_at: new Date().toISOString()
      });

      throw error;
    }
  }

  async getSessionProgress(sessionId: string): Promise<LoopProgress[]> {
    const logs = await supabaseService.getSessionLogs(sessionId);
    
    return logs.map(log => ({
      sessionId,
      iteration: log.iteration,
      plan: log.plan,
      critique: log.critique,
      score: log.score,
      patch: log.patch,
      isComplete: log.score >= 0.95
    }));
  }
}

export const reactorLoopService = new ReactorLoopService();

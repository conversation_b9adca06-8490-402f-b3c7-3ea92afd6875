
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Code, Eye } from 'lucide-react';

interface DiffViewerProps {
  diffContent?: string;
  patch?: any;
}

export const DiffViewer = ({ diffContent, patch }: DiffViewerProps) => {
  const [viewMode, setViewMode] = useState<'diff' | 'patch' | 'preview'>('diff');

  if (!diffContent && !patch) {
    return (
      <div className="h-full bg-slate-900 flex items-center justify-center">
        <div className="text-center text-slate-500">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-800 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-slate-600 rounded-sm flex items-center justify-center">
              <div className="w-4 h-4 bg-slate-600 rounded-sm"></div>
            </div>
          </div>
          <p className="text-lg font-medium">No changes available</p>
          <p className="text-sm">Run the reactor loop to see code transformations</p>
        </div>
      </div>
    );
  }

  const renderDiffView = () => {
    if (!diffContent) return null;

    const lines = diffContent.split('\n');

    return (
      <div className="p-4 font-mono text-sm">
        <div className="space-y-0">
          {lines.map((line, index) => {
            let className = "block px-3 py-1 border-l-2 ";
            let lineNumber = "";

            if (line.startsWith('---') || line.startsWith('+++')) {
              className += "text-slate-400 font-bold bg-slate-800/50 border-l-slate-600";
            } else if (line.startsWith('@@')) {
              className += "text-indigo-400 bg-indigo-900/20 border-l-indigo-500";
              lineNumber = "@@";
            } else if (line.startsWith('+')) {
              className += "text-green-400 bg-green-900/20 border-l-green-500";
              lineNumber = "+";
            } else if (line.startsWith('-')) {
              className += "text-red-400 bg-red-900/20 border-l-red-500";
              lineNumber = "-";
            } else {
              className += "text-slate-300 border-l-slate-700";
              lineNumber = " ";
            }

            return (
              <div key={index} className={className}>
                <span className="inline-block w-6 text-center text-xs opacity-60 mr-2">
                  {lineNumber}
                </span>
                <span className="whitespace-pre-wrap">
                  {line.replace(/^[+\-\s]/, '') || ' '}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderPatchView = () => {
    if (!patch) return null;

    return (
      <div className="p-4">
        <div className="space-y-4">
          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-slate-300 mb-2">Patch Description</h3>
            <p className="text-sm text-slate-400">{patch.description || 'No description available'}</p>
            {patch.confidence && (
              <div className="mt-2">
                <Badge className="bg-indigo-600/20 text-indigo-300 border-indigo-500/30">
                  Confidence: {(patch.confidence * 100).toFixed(1)}%
                </Badge>
              </div>
            )}
          </div>

          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-slate-300 mb-2">Operations</h3>
            <div className="space-y-2">
              {patch.operations?.map((op: any, index: number) => (
                <div key={index} className="bg-slate-900 rounded p-3 font-mono text-xs">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="outline" className="text-xs">
                      {op.op.toUpperCase()}
                    </Badge>
                    <span className="text-slate-400">{op.path}</span>
                  </div>
                  {op.value && (
                    <pre className="text-slate-300 whitespace-pre-wrap">
                      {JSON.stringify(op.value, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full bg-slate-900 flex flex-col">
      {/* View Mode Tabs */}
      <div className="flex items-center space-x-1 p-2 border-b border-slate-800">
        <Button
          variant={viewMode === 'diff' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('diff')}
          disabled={!diffContent}
          className="text-xs"
        >
          <FileText className="w-3 h-3 mr-1" />
          Diff
        </Button>
        <Button
          variant={viewMode === 'patch' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('patch')}
          disabled={!patch}
          className="text-xs"
        >
          <Code className="w-3 h-3 mr-1" />
          Patch
        </Button>
        <Button
          variant={viewMode === 'preview' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setViewMode('preview')}
          disabled={!patch}
          className="text-xs"
        >
          <Eye className="w-3 h-3 mr-1" />
          Preview
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'diff' && renderDiffView()}
        {viewMode === 'patch' && renderPatchView()}
        {viewMode === 'preview' && (
          <div className="p-4 text-center text-slate-500">
            <p>Preview mode coming soon</p>
          </div>
        )}
      </div>
    </div>
  );
};

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Development fallback values
const isDevelopment = import.meta.env.DEV;
const fallbackUrl = 'https://placeholder.supabase.co';
const fallbackKey = 'placeholder-key';

if (!supabaseUrl || !supabaseAnonKey) {
  if (isDevelopment) {
    console.warn('⚠️ Missing Supabase environment variables. Using fallback values for development.');
    console.warn('Please copy .env.example to .env and add your Supabase credentials.');
  } else {
    throw new Error('Missing Supabase environment variables. Please check your deployment configuration.');
  }
}

export const supabase = createClient(
  supabaseUrl || fallbackUrl,
  supabaseAnonKey || fallbackKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  }
);

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = () => {
  return !!(supabaseUrl && supabaseAnonKey &&
    supabaseUrl !== fallbackUrl &&
    supabaseAnonKey !== fallbackKey);
};

// Database types
export interface Transformation {
  id: string;
  user_id: string;
  prompt: string;
  final_patch: any;
  final_score: number;
  iterations_count: number;
  status: 'running' | 'completed' | 'failed' | 'timeout';
  github_pr_url?: string;
  created_at: string;
  completed_at?: string;
}

export interface AgentLog {
  id: string;
  transformation_id: string;
  iteration: number;
  agent_type: 'planner' | 'critic';
  input_data: any;
  output_data: any;
  score?: number;
  reasoning?: string;
  created_at: string;
}

export interface Settings {
  id: string;
  user_id: string;
  max_iterations: number;
  score_threshold: number;
  telemetry_enabled: boolean;
  preferred_model: string;
  created_at: string;
  updated_at: string;
}

export interface GitHubToken {
  id: string;
  user_id: string;
  access_token: string;
  refresh_token?: string;
  token_type: string;
  scope?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

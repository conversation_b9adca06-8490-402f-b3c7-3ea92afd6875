import { Router } from 'express';
import { z } from 'zod';
import { reactorLoopService } from '../services/reactorLoop.js';
import { supabaseService } from '../services/supabase.js';
import { createGitHubService } from '../services/githubService.js';

const router = Router();

// Request validation schemas
const loopRequestSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required'),
  maxLoops: z.number().int().min(1).max(50).optional().default(10),
  context: z.record(z.any()).optional(),
  createPR: z.boolean().optional().default(false)
});

const sessionIdSchema = z.object({
  sessionId: z.string().uuid('Invalid session ID format')
});

// POST /api/loop - Start a new reactor loop
router.post('/loop', async (req, res) => {
  try {
    const validatedRequest = loopRequestSchema.parse(req.body);
    
    console.log(`🚀 Starting reactor loop for prompt: "${validatedRequest.prompt}"`);
    
    const result = await reactorLoopService.runLoop(validatedRequest);
    
    res.json({
      success: true,
      data: {
        diff: result.finalPatch,
        score: result.score,
        iterations: result.iterations,
        logs: result.logs,
        prUrl: result.prUrl
      }
    });
  } catch (error) {
    console.error('Loop execution error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/loop/:sessionId/progress - Get progress for a session
router.get('/loop/:sessionId/progress', async (req, res) => {
  try {
    const { sessionId } = sessionIdSchema.parse(req.params);
    
    const progress = await reactorLoopService.getSessionProgress(sessionId);
    
    res.json({
      success: true,
      data: progress
    });
  } catch (error) {
    console.error('Progress fetch error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid session ID',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/loop/:sessionId - Get session details
router.get('/loop/:sessionId', async (req, res) => {
  try {
    const { sessionId } = sessionIdSchema.parse(req.params);
    
    const session = await supabaseService.getSession(sessionId);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }
    
    res.json({
      success: true,
      data: session
    });
  } catch (error) {
    console.error('Session fetch error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid session ID',
        details: error.errors
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/loop/stream - Start a streaming loop (WebSocket alternative)
router.post('/loop/stream', async (req, res) => {
  try {
    const validatedRequest = loopRequestSchema.parse(req.body);
    
    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    console.log(`🌊 Starting streaming reactor loop for prompt: "${validatedRequest.prompt}"`);
    
    const result = await reactorLoopService.runLoop(
      validatedRequest,
      (progress) => {
        // Send progress updates via SSE
        res.write(`data: ${JSON.stringify({
          type: 'progress',
          data: progress
        })}\n\n`);
      }
    );
    
    // Send final result
    res.write(`data: ${JSON.stringify({
      type: 'complete',
      data: {
        diff: result.finalPatch,
        score: result.score,
        iterations: result.iterations
      }
    })}\n\n`);
    
    res.end();
  } catch (error) {
    console.error('Streaming loop error:', error);
    
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    })}\n\n`);
    
    res.end();
  }
});

// POST /api/loop/:sessionId/pr - Create PR for a completed session
router.post('/loop/:sessionId/pr', async (req, res) => {
  try {
    const { sessionId } = sessionIdSchema.parse(req.params);

    const session = await supabaseService.getSession(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    if (session.status !== 'completed') {
      return res.status(400).json({
        success: false,
        error: 'Session must be completed to create PR'
      });
    }

    if (!session.final_patch) {
      return res.status(400).json({
        success: false,
        error: 'Session has no final patch to create PR from'
      });
    }

    const githubService = createGitHubService();
    if (!githubService) {
      return res.status(503).json({
        success: false,
        error: 'GitHub integration not configured'
      });
    }

    console.log(`🔀 Creating PR for session ${sessionId}`);

    const prResult = await githubService.createReactorPR(
      sessionId,
      session.final_patch,
      session.prompt,
      session.final_score || 0,
      session.iterations_count || 0
    );

    // Update session with PR URL
    await supabaseService.updateSession(sessionId, {
      github_pr_url: prResult.url
    });

    console.log(`✅ PR created: ${prResult.url}`);

    res.json({
      success: true,
      data: prResult
    });
  } catch (error) {
    console.error('PR creation error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid session ID',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as loopRouter };

export interface LoopRequest {
  prompt: string;
  maxLoops?: number;
  context?: Record<string, any>;
}

export interface LoopProgress {
  sessionId: string;
  iteration: number;
  plan: string;
  critique: string;
  score: number;
  patch: any;
  isComplete: boolean;
}

export interface LoopResult {
  diff: any;
  score: number;
  iterations: number;
  logs: Array<{
    iteration: number;
    plan: string;
    critique: string;
    score: number;
    patch: any;
  }>;
}

export class ReactorApiService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3001/api') {
    this.baseUrl = baseUrl;
  }

  async runLoop(request: LoopRequest): Promise<LoopResult> {
    const response = await fetch(`${this.baseUrl}/loop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to run loop');
    }

    const result = await response.json();
    return result.data;
  }

  async runStreamingLoop(
    request: LoopRequest,
    onProgress: (progress: LoopProgress) => void,
    onComplete: (result: LoopResult) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/loop/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'progress':
                  onProgress(data.data);
                  break;
                case 'complete':
                  onComplete(data.data);
                  break;
                case 'error':
                  onError(data.error);
                  break;
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async getSessionProgress(sessionId: string): Promise<LoopProgress[]> {
    const response = await fetch(`${this.baseUrl}/loop/${sessionId}/progress`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get session progress');
    }

    const result = await response.json();
    return result.data;
  }

  async getSession(sessionId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/loop/${sessionId}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get session');
    }

    const result = await response.json();
    return result.data;
  }
}

export const reactorApi = new ReactorApiService();

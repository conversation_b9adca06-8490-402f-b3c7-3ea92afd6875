import { useState, useCallback, useRef } from 'react';
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface LoopRequest {
  prompt: string;
  maxIterations?: number;
  scoreThreshold?: number;
  context?: Record<string, any>;
}

interface LoopResult {
  success: boolean;
  finalPatch: any;
  finalScore: number;
  iterations: number;
  logs: any[];
  completed: boolean;
}

interface StreamEvent {
  id: string;
  timestamp: Date;
  type: 'plan' | 'critique' | 'iteration' | 'complete' | 'error';
  content: string;
  score?: number;
  iteration?: number;
  patch?: any;
}

interface UseSupabaseReactorReturn {
  isRunning: boolean;
  events: StreamEvent[];
  currentPatch: any;
  finalResult: LoopResult | null;
  progress: number;
  transformationId: string | null;
  runLoop: (prompt: string, maxIterations?: number) => Promise<void>;
  stopLoop: () => void;
  clearEvents: () => void;
  createPR: () => Promise<void>;
}

export const useSupabaseReactor = (): UseSupabaseReactorReturn => {
  const [isRunning, setIsRunning] = useState(false);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [currentPatch, setCurrentPatch] = useState<any>(null);
  const [finalResult, setFinalResult] = useState<LoopResult | null>(null);
  const [progress, setProgress] = useState(0);
  const [transformationId, setTransformationId] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const addEvent = useCallback((event: Omit<StreamEvent, 'id' | 'timestamp'>) => {
    const newEvent: StreamEvent = {
      ...event,
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date()
    };
    setEvents(prev => [...prev, newEvent]);
  }, []);

  const runLoop = useCallback(async (prompt: string, maxIterations: number = 10) => {
    if (isRunning) return;

    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      addEvent({
        type: 'error',
        content: '⚠️ Supabase not configured. Please add your Supabase credentials to .env file. Using mock response for development.'
      });

      // Simulate a successful response for development
      setTimeout(() => {
        const mockResult = {
          success: true,
          finalPatch: {
            operations: [
              { op: 'add', path: '/mock', value: 'This is a mock transformation for development' }
            ],
            description: 'Mock transformation - configure Supabase for real AI processing'
          },
          finalScore: 0.95,
          iterations: 1,
          logs: [{
            iteration: 1,
            plan: 'Mock plan for development',
            critique: 'Mock critique - configure Supabase for real processing',
            score: 0.95,
            patch: { operations: [] }
          }],
          completed: true
        };

        setFinalResult(mockResult);
        setCurrentPatch(mockResult.finalPatch);
        setProgress(100);

        addEvent({
          type: 'complete',
          content: '✅ Mock reactor loop completed! Configure Supabase for real AI processing.',
          score: 0.95
        });

        setIsRunning(false);
      }, 2000);

      return;
    }

    setIsRunning(true);
    setEvents([]);
    setCurrentPatch(null);
    setFinalResult(null);
    setProgress(0);
    setTransformationId(null);

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    addEvent({
      type: 'iteration',
      content: `🚀 Starting Metamorphic Reactor loop with prompt: "${prompt}"`,
      iteration: 0
    });

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      // Create transformation record
      const { data: transformation, error: createError } = await supabase
        .from('transformations')
        .insert({
          user_id: session.user.id,
          prompt,
          status: 'running'
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create transformation: ${createError.message}`);
      }

      setTransformationId(transformation.id);

      addEvent({
        type: 'iteration',
        content: `📝 Created transformation record: ${transformation.id}`,
        iteration: 0
      });

      // Call the AI loop Edge Function
      const response = await fetch('/functions/v1/ai-loop', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          maxIterations,
          scoreThreshold: 0.95,
          context: {}
        }),
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run AI loop');
      }

      const result: LoopResult = await response.json();
      setFinalResult(result);
      setCurrentPatch(result.finalPatch);
      setProgress(100);

      // Update transformation record
      await supabase
        .from('transformations')
        .update({
          final_patch: result.finalPatch,
          final_score: result.finalScore,
          iterations_count: result.iterations,
          status: result.completed ? 'completed' : 'failed',
          completed_at: new Date().toISOString()
        })
        .eq('id', transformation.id);

      // Add events from the result logs
      result.logs.forEach((log, index) => {
        if (log.error) {
          addEvent({
            type: 'error',
            content: `❌ Error in iteration ${log.iteration}: ${log.error}`,
            iteration: log.iteration
          });
        } else {
          addEvent({
            type: 'plan',
            content: `🤖 Plan ${log.iteration}: ${log.plan}`,
            iteration: log.iteration
          });
          
          addEvent({
            type: 'critique',
            content: `🔍 Critique ${log.iteration}: ${log.critique}`,
            score: log.score,
            iteration: log.iteration,
            patch: log.patch
          });
        }
      });

      addEvent({
        type: 'complete',
        content: `✅ Reactor loop completed! Final score: ${(result.finalScore * 100).toFixed(1)}% (${result.iterations} iterations)`,
        score: result.finalScore
      });

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        addEvent({
          type: 'error',
          content: '⏹️ Reactor loop stopped by user'
        });
      } else {
        addEvent({
          type: 'error',
          content: `❌ Failed to run reactor loop: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
      setProgress(0);
    } finally {
      setIsRunning(false);
      abortControllerRef.current = null;
    }
  }, [isRunning, addEvent]);

  const stopLoop = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setIsRunning(false);
    setProgress(0);
    
    addEvent({
      type: 'error',
      content: '⏹️ Reactor loop stopped by user'
    });
  }, [addEvent]);

  const clearEvents = useCallback(() => {
    setEvents([]);
    setCurrentPatch(null);
    setFinalResult(null);
    setProgress(0);
    setTransformationId(null);
  }, []);

  const createPR = useCallback(async () => {
    if (!transformationId) {
      throw new Error('No transformation available for PR creation');
    }

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/functions/v1/create-pr', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transformationId
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create PR');
      }

      const result = await response.json();
      
      addEvent({
        type: 'complete',
        content: `🎉 Pull Request created successfully: ${result.pr.url}`
      });

      return result;
    } catch (error) {
      addEvent({
        type: 'error',
        content: `❌ Failed to create PR: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
      throw error;
    }
  }, [transformationId, addEvent]);

  return {
    isRunning,
    events,
    currentPatch,
    finalResult,
    progress,
    transformationId,
    runLoop,
    stopLoop,
    clearEvents,
    createPR
  };
};

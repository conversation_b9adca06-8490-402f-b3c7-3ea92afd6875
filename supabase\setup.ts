import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  try {
    console.log('🚀 Setting up Metamorphic Reactor database...');
    
    // Read and execute schema
    const schemaPath = join(process.cwd(), 'supabase', 'schema.sql');
    const schema = readFileSync(schemaPath, 'utf-8');
    
    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`📝 Executing: ${statement.substring(0, 50)}...`);
        
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        });
        
        if (error) {
          console.warn(`⚠️  Warning: ${error.message}`);
          // Continue with other statements
        }
      }
    }
    
    console.log('✅ Database setup completed successfully!');
    
    // Test the setup by creating a sample session
    console.log('🧪 Testing database setup...');
    
    const { data: testSession, error: sessionError } = await supabase
      .from('reactor_sessions')
      .insert({
        prompt: 'Test setup prompt',
        max_loops: 5,
        status: 'completed'
      })
      .select()
      .single();
    
    if (sessionError) {
      throw new Error(`Session test failed: ${sessionError.message}`);
    }
    
    const { data: testLog, error: logError } = await supabase
      .from('agent_logs')
      .insert({
        session_id: testSession.id,
        iteration: 1,
        plan: 'Test plan',
        critique: 'Test critique',
        score: 0.95,
        patch: { operations: [], description: 'Test patch', confidence: 0.95 }
      })
      .select()
      .single();
    
    if (logError) {
      throw new Error(`Log test failed: ${logError.message}`);
    }
    
    // Clean up test data
    await supabase.from('agent_logs').delete().eq('id', testLog.id);
    await supabase.from('reactor_sessions').delete().eq('id', testSession.id);
    
    console.log('✅ Database test completed successfully!');
    console.log('🎉 Metamorphic Reactor is ready to go!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase();
}

export { setupDatabase };
